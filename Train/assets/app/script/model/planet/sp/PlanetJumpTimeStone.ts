import PlanetEmptyNode from "../PlanetEmptyNode";

type shadowData = {
    x: number,
    y: number,
    index: number
}

const INDEX_START = 10000

export default class Planet<PERSON>umpTimeStone extends PlanetEmptyNode {
    public shadows: shadowData[] = []
    public shadowIndex: number = 0

    public toDB() {
        const data: any = {
            id: this.id,
            progress: this.progress,
        }
        data.shadows = this.shadows
        data.shadowIndex = this.shadowIndex
        return data
    }

    public fromDB(data: any) {
        this.progress = this.progress || data.progress || 0
        this.shadowIndex = data.shadowIndex || 0
        this.shadows = data.shadows || []
    }

    public isVirtualIndex(index: number) {
        return index >= INDEX_START
    }
    public nextShadowIndex(): number {
        const max = this.shadows.max(shadow => shadow.index)
        return max ? max.index + 1 : INDEX_START
    }

    public shiftShadow(data: shadowData) {
        const index = this.shadows.findIndex(shadow => shadow == data)
        if (index > -1) {
            const item = this.shadows.splice(index, 1)[0]
            if (item.index == this.shadowIndex) {
                this.shadowIndex = 0
            }
        }
    }

}
